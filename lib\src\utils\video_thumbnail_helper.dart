import 'dart:io';
import 'dart:typed_data';
import 'package:video_player/video_player.dart';

class VideoThumbnailHelper {
  /// 生成视频缩略图
  static Future<Uint8List?> generateThumbnail(String videoPath) async {
    try {
      // 注意：这里可以使用 fc_native_video_thumbnail 插件来生成真正的缩略图
      // 但为了简化，我们暂时返回null，让UI使用视频文件本身作为预览
      return null;
    } catch (e) {
      print('生成视频缩略图失败: $e');
      return null;
    }
  }
  
  /// 检查文件是否为视频
  static bool isVideoFile(String filePath) {
    final String fileName = filePath.toLowerCase();
    return fileName.endsWith('.mp4') || 
           fileName.endsWith('.mov') ||
           fileName.endsWith('.avi') || 
           fileName.endsWith('.mkv') ||
           fileName.endsWith('.3gp') || 
           fileName.endsWith('.webm') ||
           fileName.endsWith('.m4v') ||
           fileName.endsWith('.flv');
  }
  
  /// 获取视频时长
  static Future<Duration?> getVideoDuration(String videoPath) async {
    try {
      VideoPlayerController controller;
      
      if (videoPath.startsWith('http')) {
        controller = VideoPlayerController.networkUrl(Uri.parse(videoPath));
      } else {
        controller = VideoPlayerController.file(File(videoPath));
      }
      
      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();
      
      return duration;
    } catch (e) {
      print('获取视频时长失败: $e');
      return null;
    }
  }
  
  /// 格式化时长显示
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
    } else {
      return "$twoDigitMinutes:$twoDigitSeconds";
    }
  }
}
